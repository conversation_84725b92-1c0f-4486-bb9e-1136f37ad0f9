package com.compatibility.homes;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.core.BlockPos;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.network.chat.Component;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.RegisterCommandsEvent;
import net.minecraftforge.event.server.ServerStartedEvent;
import net.minecraftforge.event.server.ServerStoppingEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.lang.reflect.Type;
import java.util.*;

@Mod("homes")
public class homes {
    private final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    private final Map<UUID, BlockPos> homes = new HashMap<>();
    private final Map<String, BlockPos> warps = new HashMap<>();
    private File homesFile, warpsFile;

    public homes() {
        FMLJavaModLoadingContext.get().getModEventBus().addListener(this::setup);
        MinecraftForge.EVENT_BUS.register(this);
    }

    private void setup(final FMLCommonSetupEvent event) {}

    @SubscribeEvent
    public void onServerStarted(ServerStartedEvent event) {
        MinecraftServer server = event.getServer();
        File dir = new File(server.getServerDirectory(), "serverconfig");
        if (!dir.exists()) dir.mkdirs();
        homesFile = new File(dir, "warpmod_homes.json");
        warpsFile = new File(dir, "warpmod_warps.json");
        loadHomes(); loadWarps();
    }

    @SubscribeEvent
    public void onServerStopping(ServerStoppingEvent event) {
        saveHomes(); saveWarps();
    }

    @SubscribeEvent
    public void onRegisterCommands(RegisterCommandsEvent event) {
        var d = event.getDispatcher();

        d.register(Commands.literal("sethome").executes(ctx -> {
            ServerPlayer p = ctx.getSource().getPlayerOrException();
            homes.put(p.getUUID(), p.blockPosition());
            saveHomes();
            ctx.getSource().sendSuccess(() -> Component.literal("Home set!"), false);
            return 1;
        }));

        d.register(Commands.literal("home").executes(ctx -> {
            ServerPlayer p = ctx.getSource().getPlayerOrException();
            BlockPos pos = homes.get(p.getUUID());
            if (pos != null) {
                p.teleportTo(pos.getX(), pos.getY(), pos.getZ());
                ctx.getSource().sendSuccess(() -> Component.literal("Teleported home!"), false);
            } else {
                ctx.getSource().sendFailure(Component.literal("Home not set."));
            }
            return 1;
        }));

        d.register(Commands.literal("delhome").executes(ctx -> {
            ServerPlayer p = ctx.getSource().getPlayerOrException();
            if (homes.remove(p.getUUID()) != null) {
                saveHomes();
                ctx.getSource().sendSuccess(() -> Component.literal("Home deleted."), false);
            } else {
                ctx.getSource().sendFailure(Component.literal("No home to delete."));
            }
            return 1;
        }));

        d.register(Commands.literal("setwarp")
                .then(Commands.argument("name", net.minecraft.commands.arguments.StringArgumentType.word())
                        .executes(ctx -> {
                            String name = net.minecraft.commands.arguments.StringArgumentType.getString(ctx, "name").toLowerCase();
                            ServerPlayer p = ctx.getSource().getPlayerOrException();
                            warps.put(name, p.blockPosition());
                            saveWarps();
                            ctx.getSource().sendSuccess(() -> Component.literal("Warp '" + name + "' set."), false);
                            return 1;
                        })));

        d.register(Commands.literal("warp")
                .then(Commands.argument("name", net.minecraft.commands.arguments.StringArgumentType.word())
                        .executes(ctx -> {
                            String name = net.minecraft.commands.arguments.StringArgumentType.getString(ctx, "name").toLowerCase();
                            BlockPos pos = warps.get(name);
                            ServerPlayer p = ctx.getSource().getPlayerOrException();
                            if (pos != null) {
                                p.teleportTo(pos.getX(), pos.getY(), pos.getZ());
                                ctx.getSource().sendSuccess(() -> Component.literal("Warped to '" + name + "'!"), false);
                            } else {
                                ctx.getSource().sendFailure(Component.literal("Warp '" + name + "' not found."));
                            }
                            return 1;
                        })));

        d.register(Commands.literal("delwarp")
                .then(Commands.argument("name", net.minecraft.commands.arguments.StringArgumentType.word())
                        .executes(ctx -> {
                            String name = net.minecraft.commands.arguments.StringArgumentType.getString(ctx, "name").toLowerCase();
                            if (warps.remove(name) != null) {
                                saveWarps();
                                ctx.getSource().sendSuccess(() -> Component.literal("Warp '" + name + "' deleted."), false);
                            } else {
                                ctx.getSource().sendFailure(Component.literal("Warp not found."));
                            }
                            return 1;
                        })));

        d.register(Commands.literal("warps").executes(ctx -> {
            if (warps.isEmpty()) {
                ctx.getSource().sendSuccess(() -> Component.literal("No warps available."), false);
            } else {
                String list = String.join(", ", warps.keySet());
                ctx.getSource().sendSuccess(() -> Component.literal("Warps: " + list), false);
            }
            return 1;
        }));
    }

    private void saveHomes() {
        try (FileWriter w = new FileWriter(homesFile)) {
            Map<String, int[]> map = new HashMap<>();
            homes.forEach((u, p) -> map.put(u.toString(), new int[]{p.getX(), p.getY(), p.getZ()}));
            gson.toJson(map, w);
        } catch (Exception e) { e.printStackTrace(); }
    }

    private void loadHomes() {
        if (!homesFile.exists()) return;
        try (FileReader r = new FileReader(homesFile)) {
            Type t = new TypeToken<Map<String, int[]>>(){}.getType();
            Map<String, int[]> data = gson.fromJson(r, t);
            homes.clear();
            data.forEach((u, a) -> homes.put(UUID.fromString(u), new BlockPos(a[0], a[1], a[2])));
        } catch (Exception e) { e.printStackTrace(); }
    }

    private void saveWarps() {
        try (FileWriter w = new FileWriter(warpsFile)) {
            Map<String, int[]> map = new HashMap<>();
            warps.forEach((n, p) -> map.put(n, new int[]{p.getX(), p.getY(), p.getZ()}));
            gson.toJson(map, w);
        } catch (Exception e) { e.printStackTrace(); }
    }

    private void loadWarps() {
        if (!warpsFile.exists()) return;
        try (FileReader r = new FileReader(warpsFile)) {
            Type t = new TypeToken<Map<String, int[]>>(){}.getType();
            Map<String, int[]> data = gson.fromJson(r, t);
            warps.clear();
            data.forEach((n, a) -> warps.put(n, new BlockPos(a[0], a[1], a[2])));
        } catch (Exception e) { e.printStackTrace(); }
    }
}
